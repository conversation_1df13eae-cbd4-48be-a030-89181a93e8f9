# Deepseek 在线模型 API 的配置
DEEPSEEK_API_KEY=sk-xxx
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-chat

# 视觉模型配置，用来处理图片的解析
VISION_API_KEY=sk-xxxx
VISION_BASE_URL=https://ai.devtool.tech/proxy/v1
VISION_MODEL=gpt-4o

# 通过 Ollama 本地部署的模型
OLLAMA_BASE_URL=http://192.168.110.131:11434  # 注意：这里需要根据实际情况修改

OLLAMA_CHAT_MODEL=qwen2.5:32b   # 聊天模型
OLLAMA_REASON_MODEL=deepseek-r1:32b  # 推理模型
OLLAMA_AGENT_MODEL=qwen2.5:32b  # Agent模型
OLLAMA_EMBEDDING_MODEL=bge-m3  # 词向量模型

# 模型服务选择
CHAT_SERVICE=deepseek  # 或 ollama， 选择哪个模型服务，就会加载对应哪个模型的 API Key，Base URL，Model 等配置
REASON_SERVICE=ollama  # 或 deepseek，如果选择 Deepseek 模型服务，则需要将 DEEPSEEK_MODEL 设置为 deepseek-reason
AGENT_SERVICE=deepseek  # 或 ollama    

# SerpAPI配置, 联网搜索的 API，注册地址：https://serpapi.com/
SERPAPI_KEY=xxxxx
SEARCH_RESULT_COUNT=10   # 联网搜索结果的数量

# 本地Mysql数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=g1601522830
DB_NAME=assist_gen

# 本地Neo4j数据库配置
NEO4J_URL=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=g1601522830
NEO4J_DATABASE=neo4j

# 本地Redis缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_CACHE_EXPIRE=3600
REDIS_CACHE_THRESHOLD=0.90  # 缓存阈值

# Microsoft GraphRAG 配置
GRAPHRAG_PROJECT_DIR=  # GraphRAG项目目录
GRAPHRAG_DATA_DIR=data                         # 数据目录名称
GRAPHRAG_QUERY_TYPE=local                      # 查询类型: local, global, drift, basic
GRAPHRAG_RESPONSE_TYPE=text                    # 响应类型: text
GRAPHRAG_COMMUNITY_LEVEL=3                     # 社区级别
GRAPHRAG_DYNAMIC_COMMUNITY=false               # 是否动态选择社区