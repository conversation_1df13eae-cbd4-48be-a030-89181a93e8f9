"""搜索服务的提示词管理"""
from datetime import datetime

# 基础系统提示
SEARCH_SYSTEM_PROMPT = """你是一个智能助手，可以通过调用外部的工具获取实时信息。
你可以使用的工具及使用方法如下：

{tools_description}  

当你遇到以下情况时，请调用联网检索工具：
1. 问题涉及实时数据（如天气、新闻、股票）。
2. 问题涉及最新事件或动态信息（如'今天发生了什么？'）。
3. 问题涉及特定领域的外部知识（如法律、医疗、科技）。
4. 问题中包含'最新'、'当前'、'今天'等时间敏感关键词。
其他情况下，请直接回答用户的问题。"""

# 搜索结果总结提示
SEARCH_SUMMARY_PROMPT = """# 以下内容是基于用户发送的消息的搜索结果:

{context}

每个搜索结果包含标题、链接和内容。在引用来源时，请使用以下固定格式：
- 引用格式：[标题](链接)
- 示例：根据最新研究显示，人工智能正在快速发展 [2024年AI发展报告](http://example.com)
- 引用必须放在相关内容的末尾，不要集中在最后
- 如果一段内容来自多个来源，请在末尾列出所有来源，用空格分隔，如：[标题1](链接1) [标题2](链接2)

在回答时，请注意以下几点：

今天是{cur_date}。
1. 并非搜索结果的所有内容都与用户的问题密切相关，你需要结合问题，对搜索结果进行甄别、筛选。
2. 对于列举类的问题，尽量将答案控制在10个要点以内，并告诉用户可以查看搜索来源、获得完整信息。
3. 对于创作类的问题，请务必在每个段落末尾使用规定格式引用来源。
4. 如果回答很长，请尽量结构化、分段落总结。如果需要分点作答，尽量控制在5个点以内。
5. 对于客观类的问答，如果答案非常简短，可以适当补充相关信息。
6. 你需要根据用户要求和回答内容选择合适、美观的回答格式，确保可读性强。
7. 你的回答应该综合多个相关网页来回答，不要重复引用同一个来源。

用户问题：{query}"""

def format_search_context(search_results: list, start_index: int = 1) -> str:
    """格式化搜索结果为上下文"""
    formatted_results = []
    for i, result in enumerate(search_results, start=start_index):
        formatted_results.append(
            f"[webpage {i} begin]\n"
            f"标题：{result['title']}\n"
            f"链接：{result['url']}\n"
            f"内容：{result['snippet']}\n"
            f"[webpage {i} end]"
        )
    return "\n\n".join(formatted_results) 